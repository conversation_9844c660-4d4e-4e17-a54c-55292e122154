#!/usr/bin/env python3
"""
Memory efficiency tests comparing our streaming JSON reader with DuckDB's default JSON reader.
Tests the core goal: avoid reading data if it's not needed by the user's query to keep memory usage low.
"""

import duckdb
import json
import tempfile
import os
import pytest
import psutil
import time
from pathlib import Path

class TestMemoryEfficiency:
    """Test memory efficiency compared to DuckDB's default JSON reader."""
    
    @pytest.fixture
    def duckdb_conn(self):
        """Create a DuckDB connection with our extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        return conn
    
    @pytest.fixture
    def duckdb_conn_default(self):
        """Create a DuckDB connection without our extension (uses default JSON reader)."""
        return duckdb.connect()
    
    @pytest.fixture
    def temp_json_file(self):
        """Create a temporary JSON file for testing."""
        fd, path = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        yield path
        if os.path.exists(path):
            os.unlink(path)
    
    def create_json_file(self, data, filepath):
        """Helper to create JSON test files."""
        with open(filepath, 'w') as f:
            json.dump(data, f)
    
    def measure_memory_usage(self, func):
        """Measure peak memory usage during function execution."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Run the function
        result = func()
        
        # Force garbage collection and measure peak
        import gc
        gc.collect()
        peak_memory = process.memory_info().rss
        
        memory_used = peak_memory - initial_memory
        return result, memory_used
    
    @pytest.mark.skip(reason="segfault")
    def test_projection_pushdown_memory_efficiency(self, duckdb_conn, duckdb_conn_default, temp_json_file):
        """Test memory efficiency when selecting only specific fields from large objects."""
        
        # Create large JSON objects with many fields, but we'll only select one
        large_objects = []
        for i in range(1000):
            obj = {
                "target_field": f"value_{i}",  # This is what we'll select
                "large_data": "x" * 10000,     # 10KB of unused data per object
                "unused_array": list(range(1000)),  # Large unused array
                "unused_nested": {
                    "deep": {
                        "structure": {
                            "with": {
                                "lots": {
                                    "of": {
                                        "data": "y" * 5000
                                    }
                                }
                            }
                        }
                    }
                }
            }
            large_objects.append(obj)
        
        self.create_json_file(large_objects, temp_json_file)
        
        print(f"\nTest file size: {os.path.getsize(temp_json_file) / 1024 / 1024:.2f} MB")
        
        # Test our extension (should use minimal memory due to projection pushdown)
        def test_our_extension():
            return duckdb_conn.execute(f'SELECT target_field FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        # Test DuckDB default (should use more memory as it reads everything)
        def test_default_reader():
            return duckdb_conn_default.execute(f"SELECT target_field FROM read_json_auto('{temp_json_file}')").fetchall()
        
        our_result, our_memory = self.measure_memory_usage(test_our_extension)
        default_result, default_memory = self.measure_memory_usage(test_default_reader)
        
        print(f"Our extension memory usage: {our_memory / 1024 / 1024:.2f} MB")
        print(f"Default reader memory usage: {default_memory / 1024 / 1024:.2f} MB")
        print(f"Memory efficiency ratio: {default_memory / max(our_memory, 1):.2f}x")
        
        # Verify results are the same
        assert len(our_result) == len(default_result) == 1000
        assert our_result[0][0] == "value_0"
        assert default_result[0][0] == "value_0"
        
        # Our extension should use significantly less memory
        # Allow some tolerance, but expect at least 2x improvement
        assert our_memory < default_memory / 2, f"Expected significant memory savings, got our={our_memory}, default={default_memory}"
    
    def test_large_array_first_element_only(self, duckdb_conn, duckdb_conn_default, temp_json_file):
        """Test memory efficiency when selecting only the first element of large arrays."""
        
        # Create objects with very large arrays, but we'll only access the first element
        data = []
        for i in range(100):
            obj = {
                "id": i,
                "large_array": list(range(10000)),  # 10K element array
                "metadata": {
                    "description": "Large object with big array",
                    "unused_data": "z" * 5000
                }
            }
            data.append(obj)
        
        self.create_json_file(data, temp_json_file)
        
        print(f"\nTest file size: {os.path.getsize(temp_json_file) / 1024 / 1024:.2f} MB")
        
        # Test accessing only the first element of each array
        def test_our_extension():
            return duckdb_conn.execute(f'SELECT id, large_array[1] as first_element FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        def test_default_reader():
            return duckdb_conn_default.execute(f"SELECT id, large_array[1] as first_element FROM read_json_auto('{temp_json_file}')").fetchall()
        
        our_result, our_memory = self.measure_memory_usage(test_our_extension)
        default_result, default_memory = self.measure_memory_usage(test_default_reader)
        
        print(f"Our extension memory usage: {our_memory / 1024 / 1024:.2f} MB")
        print(f"Default reader memory usage: {default_memory / 1024 / 1024:.2f} MB")
        print(f"Memory efficiency ratio: {default_memory / max(our_memory, 1):.2f}x")
        
        # Verify results are the same
        assert len(our_result) == len(default_result) == 100
        assert our_result[0] == (0, 0)  # First object: id=0, first_element=0
        assert default_result[0] == (0, 0)
        
        # Our extension should use less memory (though array access might still require reading the array)
        # This test validates that we don't read unused fields like metadata
        print(f"Memory usage comparison: our={our_memory}, default={default_memory}")
    
    def test_existing_large_file_memory_efficiency(self, duckdb_conn, duckdb_conn_default):
        """Test memory efficiency with the existing large test file."""
        
        large_file = "tests/test_large.json"
        if not os.path.exists(large_file):
            pytest.skip("Large test file not found")
        
        print(f"\nLarge file size: {os.path.getsize(large_file) / 1024 / 1024:.2f} MB")
        
        # Test selecting only specific fields from the large dataset
        def test_our_extension():
            return duckdb_conn.execute(f'SELECT dataset_info.name, users[1].name FROM streaming_json_reader("{large_file}")').fetchall()
        
        def test_default_reader():
            return duckdb_conn_default.execute(f"SELECT dataset_info.name, users[1].name FROM read_json_auto('{large_file}')").fetchall()
        
        our_result, our_memory = self.measure_memory_usage(test_our_extension)
        default_result, default_memory = self.measure_memory_usage(test_default_reader)
        
        print(f"Our extension memory usage: {our_memory / 1024 / 1024:.2f} MB")
        print(f"Default reader memory usage: {default_memory / 1024 / 1024:.2f} MB")
        print(f"Memory efficiency ratio: {default_memory / max(our_memory, 1):.2f}x")
        
        # Verify results are the same
        assert our_result == default_result
        assert our_result[0][0] == "Large User Dataset"
        assert our_result[0][1] == "User_1"
        
        print(f"Memory usage comparison: our={our_memory}, default={default_memory}")
    
    @pytest.mark.skip(reason="Creates very large files - enable for stress testing")
    def test_duckdb_size_limits(self, duckdb_conn, duckdb_conn_default, temp_json_file):
        """Test scenarios that hit DuckDB's default JSON reader limits but work with our extension."""
        
        # Create JSON that approaches DuckDB's object size limits
        # DuckDB has limits on object sizes and nesting depth
        very_large_object = {
            "metadata": {"type": "stress_test"},
            "huge_field": "x" * (50 * 1024 * 1024),  # 50MB string
            "deep_nesting": {"level": 1}
        }
        
        # Create deep nesting
        current = very_large_object["deep_nesting"]
        for i in range(100):  # 100 levels deep
            current["next"] = {"level": i + 2, "data": "y" * 1000}
            current = current["next"]
        
        self.create_json_file(very_large_object, temp_json_file)
        
        print(f"\nStress test file size: {os.path.getsize(temp_json_file) / 1024 / 1024:.2f} MB")
        
        # Test that our extension can handle it
        def test_our_extension():
            return duckdb_conn.execute(f'SELECT metadata.type FROM streaming_json_reader("{temp_json_file}")').fetchall()
        
        our_result, our_memory = self.measure_memory_usage(test_our_extension)
        
        print(f"Our extension memory usage: {our_memory / 1024 / 1024:.2f} MB")
        assert our_result[0][0] == "stress_test"
        
        # Test if default reader can handle it (might fail or use excessive memory)
        try:
            def test_default_reader():
                return duckdb_conn_default.execute(f"SELECT metadata.type FROM read_json_auto('{temp_json_file}')").fetchall()
            
            default_result, default_memory = self.measure_memory_usage(test_default_reader)
            print(f"Default reader memory usage: {default_memory / 1024 / 1024:.2f} MB")
            print(f"Memory efficiency ratio: {default_memory / max(our_memory, 1):.2f}x")
            
        except Exception as e:
            print(f"Default reader failed (as expected): {e}")
            print("Our extension succeeded where default reader failed!")
